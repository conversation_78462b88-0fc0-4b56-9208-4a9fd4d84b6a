# ACO ComponentPath Erweiterung

## Übersicht
Diese Erweiterung fügt automatische Kontextinformationen zu den ACO-Rechten hinzu, um zu zeigen, wo die Rechte in der Anwendung verwendet werden.

## Implementierte Änderungen

### 1. TACOListEntry Klasse erweitert (Library\ACOList.pas)
- **Neues Feld**: `ComponentPath : String` 
- Speichert den vollständigen Pfad der Komponente durch die Parent-Hierarchie
- Beispiel: "LVSForm.WETabSheet.WEPrintButton"

### 2. Automatische Pfad-Sammlung (ACOModul.pas)
- **Neue Funktion**: `GetComponentPath(Component: TComponent): String`
- Durchläuft die Parent-Hierarchie einer Komponente
- Erstellt automatisch den Komponentenpfad beim Setzen der Berechtigungen

### 3. ACO-<PERSON> (Library\ACOListEdit.pas)
- **Neue Spalte**: "Komponenten-Pfad" im ACOStringGrid
- Zeigt den automatisch gesammelten Pfad an
- Ermöglicht manuelle Bearbeitung falls nötig

### 4. Persistierung erweitert
- ComponentPath wird in CSV-Format gespeichert und geladen
- Rückwärtskompatibel mit bestehenden ACO-Dateien

## Funktionsweise

### Automatische Pfad-Sammlung
```pascal
// Beispiel für Button-Pfad
LVSForm.WETabSheet.WEPrintButton

// Beispiel für Tab-Pfad  
LVSForm.WETabSheet

// Beispiel für Menü-Pfad
LVSForm.MainMenu.Benutzer1
```

### Wann wird der Pfad gesammelt?
- Beim Aufruf von `SetBerechtigungen()` für jede Komponente
- Nur wenn das ComponentPath-Feld noch leer ist
- Automatisch für alle unterstützten Komponententypen:
  - TButton
  - TTabSheet  
  - TMenuItem
  - TPanel

## Vorteile

### 1. Bessere Übersicht
- Zeigt sofort, wo ein Recht verwendet wird
- Hierarchische Darstellung der UI-Struktur
- Einfache Navigation zu Komponenten

### 2. Einfache Implementierung
- Minimaler Code-Aufwand
- Keine Breaking Changes
- Rückwärtskompatibel

### 3. Automatische Aktualisierung
- Pfade werden automatisch beim ersten Zugriff gesammelt
- Keine manuelle Pflege erforderlich
- Selbst-dokumentierend

## Verwendung

### Im ACO-Editor
1. Öffnen Sie den ACO-Editor
2. Die neue Spalte "Komponenten-Pfad" zeigt automatisch die Pfade
3. Pfade werden beim nächsten Anwendungsstart automatisch gefüllt

### Für Entwickler
```pascal
// Pfad einer Komponente abrufen
var
  path: String;
begin
  path := GetComponentPath(MyButton);
  // Ergebnis: "MainForm.TabSheet1.MyButton"
end;
```

## Erweiterungsmöglichkeiten

### 1. Zusätzliche Kontextinformationen
- Tab-Namen der übergeordneten Komponenten
- Formular-Titel statt nur Name
- Beschreibung der Funktionalität

### 2. Verweis-System
```pascal
// Mögliche Erweiterung
ComponentReferences : TStringList;  // "Dieses Recht wird auch in Wareneingang verwendet"
```

### 3. Automatische Dokumentation
- Export der ACO-Liste mit Pfaden
- Generierung von Benutzerhandbüchern
- Rechte-Matrix nach Bereichen

## Technische Details

### Speicherformat (CSV)
```
FormName;CompType;CompName;ACOName;ACOText;ACOID;ACOGroup;ComponentPath
LVSForm;TButton;WEPrintButton;LVSForm.WEPrintButton;WE ausdrucken;6;WE;LVSForm.WETabSheet.WEPrintButton
```

### Kompatibilität
- Bestehende ACO-Dateien funktionieren weiterhin
- ComponentPath wird bei fehlenden Daten automatisch nachgetragen
- Keine Änderungen an bestehenden Interfaces

## Installation
1. Kompilieren Sie die geänderten Units
2. Starten Sie die Anwendung neu
3. Die Pfade werden automatisch beim nächsten ACO-Check gesammelt

## Fazit
Diese Erweiterung bietet eine einfache und effektive Lösung für bessere Kontextinformationen in der ACO-Liste, ohne die bestehende Funktionalität zu beeinträchtigen.
