-- ============================================================================
-- ACO ComponentPath Datenbank-Erweiterung
-- ============================================================================
-- Die<PERSON> Script erweitert die SYS_ACO Tabelle um die COMPONENT_PATH Spalte
-- für die automatische Sammlung von Komponentenpfaden
-- ============================================================================

-- Prüfe ob die Spalte bereits existiert
DECLARE
  column_exists NUMBER := 0;
BEGIN
  SELECT COUNT(*)
  INTO column_exists
  FROM user_tab_columns
  WHERE table_name = 'SYS_ACO'
    AND column_name = 'COMPONENT_PATH';
    
  IF column_exists = 0 THEN
    -- Spalte hinzufügen
    EXECUTE IMMEDIATE 'ALTER TABLE SYS_ACO ADD COMPONENT_PATH VARCHAR2(500)';
    
    -- Kommentar hinzufügen
    EXECUTE IMMEDIATE 'COMMENT ON COLUMN SYS_ACO.COMPONENT_PATH IS ''Automatisch gesammelter Komponentenpfad durch Parent-Hierarchie''';
    
    DBMS_OUTPUT.PUT_LINE('COMPONENT_PATH Spalte erfolgreich hinzugefügt');
  ELSE
    DBMS_OUTPUT.PUT_LINE('COMPONENT_PATH Spalte existiert bereits');
  END IF;
END;
/

-- Optional: Index für bessere Performance bei Suchen
DECLARE
  index_exists NUMBER := 0;
BEGIN
  SELECT COUNT(*)
  INTO index_exists
  FROM user_indexes
  WHERE index_name = 'IDX_SYS_ACO_COMP_PATH';
    
  IF index_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE INDEX IDX_SYS_ACO_COMP_PATH ON SYS_ACO(COMPONENT_PATH)';
    DBMS_OUTPUT.PUT_LINE('Index IDX_SYS_ACO_COMP_PATH erfolgreich erstellt');
  ELSE
    DBMS_OUTPUT.PUT_LINE('Index IDX_SYS_ACO_COMP_PATH existiert bereits');
  END IF;
END;
/

-- Prüfe die Struktur der erweiterten Tabelle
SELECT column_name, data_type, data_length, nullable
FROM user_tab_columns
WHERE table_name = 'SYS_ACO'
ORDER BY column_id;

-- Beispiel-Abfrage für ACOs mit ComponentPath
SELECT 
  APPLICATION,
  TYP,
  NAME,
  OBJECT_NAME,
  COMPONENT_PATH,
  BESCHREIBUNG
FROM SYS_ACO
WHERE COMPONENT_PATH IS NOT NULL
ORDER BY APPLICATION, TYP, NAME;

COMMIT;
