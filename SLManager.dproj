﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <ProjectGuid>{fbc8298c-9613-416e-bcbc-eb55e49d3d96}</ProjectGuid>
        <MainSource>SLManager.dpr</MainSource>
        <Configuration Condition=" '$(Configuration)' == '' ">Release</Configuration>
        <DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
        <DCC_DependencyCheckOutputName>SLManager.exe</DCC_DependencyCheckOutputName>
        <FrameworkType>VCL</FrameworkType>
        <ProjectVersion>20.2</ProjectVersion>
        <Base>True</Base>
        <Config Condition="'$(Config)'==''">Release</Config>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
        <TargetedPlatforms>3</TargetedPlatforms>
        <AppType>Application</AppType>
        <ProjectName Condition="'$(ProjectName)'==''">SLManager</ProjectName>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Base)'=='true') or '$(Base_Win64)'!=''">
        <Base_Win64>true</Base_Win64>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
        <Cfg_1_Win32>true</Cfg_1_Win32>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win64)'!=''">
        <Cfg_1_Win64>true</Cfg_1_Win64>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_2)'=='true') or '$(Cfg_2_Win32)'!=''">
        <Cfg_2_Win32>true</Cfg_2_Win32>
        <CfgParent>Cfg_2</CfgParent>
        <Cfg_2>true</Cfg_2>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Cfg_2)'=='true') or '$(Cfg_2_Win64)'!=''">
        <Cfg_2_Win64>true</Cfg_2_Win64>
        <CfgParent>Cfg_2</CfgParent>
        <Cfg_2>true</Cfg_2>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <DCC_Namespace>Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;System;Xml;Data;Datasnap;Web;Soap;Winapi;VclTee;Data.Win;System.Win;$(DCC_Namespace)</DCC_Namespace>
        <VerInfo_Build>87</VerInfo_Build>
        <SanitizedProjectName>SLManager</SanitizedProjectName>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_MajorVer>4</VerInfo_MajorVer>
        <VerInfo_Keys>CompanyName=common solutions;FileDescription=storelogix manager;FileVersion=********;InternalName=SLManager;LegalCopyright=Copyright © 2004-2016 common solution;LegalTrademarks=;OriginalFilename=SLManager.exe;ProductName=storelogix;ProductVersion=4.4</VerInfo_Keys>
        <VerInfo_Locale>1031</VerInfo_Locale>
        <VerInfo_MinorVer>4</VerInfo_MinorVer>
        <VerInfo_Release>7</VerInfo_Release>
        <Icon_MainIcon>$(BDS)\bin\delphi_PROJECTICON.ico</Icon_MainIcon>
        <UWP_DelphiLogo44>$(BDS)\bin\Artwork\Windows\UWP\delphi_UwpDefault_44.png</UWP_DelphiLogo44>
        <UWP_DelphiLogo150>$(BDS)\bin\Artwork\Windows\UWP\delphi_UwpDefault_150.png</UWP_DelphiLogo150>
        <DCC_Define>LicManager;$(DCC_Define)</DCC_Define>
        <DCC_ExeOutput>.\$(Platform)\$(Config)\Exe</DCC_ExeOutput>
        <DCC_DcuOutput>.\$(Platform)\$(Config)</DCC_DcuOutput>
        <Custom_Styles>Windows10|VCLSTYLE|$(BDSCOMMONDIR)\Styles\Windows10.vsf;&quot;Windows10 Blue|VCLSTYLE|$(BDSCOMMONDIR)\Styles\Windows10Blue.vsf&quot;;&quot;Windows10 Dark|VCLSTYLE|$(BDSCOMMONDIR)\Styles\Windows10Dark.vsf&quot;;&quot;Windows10 Green|VCLSTYLE|$(BDSCOMMONDIR)\Styles\Windows10Green.vsf&quot;;&quot;Windows10 Purple|VCLSTYLE|$(BDSCOMMONDIR)\Styles\Windows10Purple.vsf&quot;;&quot;Windows10 SlateGray|VCLSTYLE|$(BDSCOMMONDIR)\Styles\Windows10SlateGray.vsf&quot;</Custom_Styles>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <Debugger_DebugSourcePath>D:\Projekte\Zimbo-LVS\FrontEnd\Library\;$(Debugger_DebugSourcePath)</Debugger_DebugSourcePath>
        <Icon_MainIcon>SLManager_Icon.ico</Icon_MainIcon>
        <Manifest_File>$(BDS)\bin\default_app.manifest</Manifest_File>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <DCC_Namespace>Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <VerInfo_Keys>CompanyName=;FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductVersion=*******;Comments=;ProgramID=com.embarcadero.$(MSBuildProjectName);FileDescription=$(MSBuildProjectName);ProductName=$(MSBuildProjectName)</VerInfo_Keys>
        <UWP_DelphiLogo44>$(BDS)\bin\Artwork\Windows\UWP\delphi_UwpDefault_44.png</UWP_DelphiLogo44>
        <UWP_DelphiLogo150>$(BDS)\bin\Artwork\Windows\UWP\delphi_UwpDefault_150.png</UWP_DelphiLogo150>
        <VerInfo_MajorVer>1</VerInfo_MajorVer>
        <DCC_UsePackage>vcl;rtl;vclactnband;vclx;dbrtl;vcldb;dacvcl250;dac250;vclimg;xmlrtl;DbxClientDriver;CustomIPTransport;dbexpress;dbxcds;$(DCC_UsePackage)</DCC_UsePackage>
        <VerInfo_MinorVer>0</VerInfo_MinorVer>
        <VerInfo_Release>0</VerInfo_Release>
        <VerInfo_Build>0</VerInfo_Build>
        <AppDPIAwarenessMode>none</AppDPIAwarenessMode>
        <PreBuildEvent><![CDATA[$(Projectdir)\set_product_version.bat
$(PreBuildEvent)]]></PreBuildEvent>
        <VerInfo_IncludeVerInfo>false</VerInfo_IncludeVerInfo>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win64)'!=''">
        <DCC_Namespace>Datasnap.Win;Web.Win;Soap.Win;Xml.Win;$(DCC_Namespace)</DCC_Namespace>
        <BT_BuildType>Debug</BT_BuildType>
        <VerInfo_Keys>CompanyName=;FileDescription=$(MSBuildProjectName);FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProgramID=com.embarcadero.$(MSBuildProjectName);ProductName=$(MSBuildProjectName);ProductVersion=*******;Comments=</VerInfo_Keys>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <Manifest_File>$(BDS)\bin\default_app.manifest</Manifest_File>
        <DCC_UsePackage>DbxClientDriver;CustomIPTransport;dbexpress;dbxcds;$(DCC_UsePackage)</DCC_UsePackage>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <Version>7.0</Version>
        <DCC_IOChecking>False</DCC_IOChecking>
        <DCC_IntegerOverflowCheck>True</DCC_IntegerOverflowCheck>
        <DCC_RangeChecking>True</DCC_RangeChecking>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
        <DCC_MapFile>3</DCC_MapFile>
        <DCC_StackSize>2097152,4194304</DCC_StackSize>
        <DCC_UnitSearchPath>.\Library;.\CR 11 VCL;.\Library\Jwa;.\Library\QR Code;.\ABCAnalyse;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
        <DCC_ResourcePath>.\Delphi 2007;.\Library\FastMM-9;.\Library;.\CR 11 VCL;.\JPG;..\..\Delphi\GraphicEx;.\Library\DCEF3\src;..\..\Delphi\PreView;$(DCC_ResourcePath)</DCC_ResourcePath>
        <DCC_ObjPath>.\Delphi 2007;.\Library\FastMM-9;.\Library;.\CR 11 VCL;.\JPG;..\..\Delphi\GraphicEx;.\Library\DCEF3\src;..\..\Delphi\PreView;$(DCC_ObjPath)</DCC_ObjPath>
        <DCC_IncludePath>.\Delphi 2007;.\Library\FastMM-9;.\Library;.\CR 11 VCL;.\JPG;..\..\Delphi\GraphicEx;.\Library\DCEF3\src;..\..\Delphi\PreView;$(DCC_IncludePath)</DCC_IncludePath>
        <DCC_AssertionsAtRuntime>False</DCC_AssertionsAtRuntime>
        <VerInfo_MinorVer>8</VerInfo_MinorVer>
        <VerInfo_Release>4</VerInfo_Release>
        <VerInfo_Build>0</VerInfo_Build>
        <VerInfo_Keys>CompanyName=common solutions;FileDescription=storelogix manager;FileVersion=*******;InternalName=SLManager;LegalCopyright=Copyright © 2004-2020 common solution;LegalTrademarks=;OriginalFilename=SLManager.exe;ProductName=storelogix;ProductVersion=4.4</VerInfo_Keys>
        <DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
        <DCC_ImportedDataReferences>false</DCC_ImportedDataReferences>
        <DCC_DebugInformation>0</DCC_DebugInformation>
        <DCC_MaxStackSize>5000000</DCC_MaxStackSize>
        <DCC_MinStackSize>1000000</DCC_MinStackSize>
        <DCC_IMPLICIT_STRING_CAST>false</DCC_IMPLICIT_STRING_CAST>
        <DCC_Description>SL-Manager</DCC_Description>
        <DCC_IMPLICIT_STRING_CAST_LOSS>false</DCC_IMPLICIT_STRING_CAST_LOSS>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
        <VerInfo_Keys>CompanyName=common solutions;FileVersion=********;InternalName=SLManager;LegalCopyright=Copyright © 2004-2024 common solution;LegalTrademarks=;OriginalFilename=;ProductVersion=4.9;Comments=;ProgramID=;FileDescription=storelogix manager;ProductName=storelogix</VerInfo_Keys>
        <BT_BuildType>Debug</BT_BuildType>
        <UWP_DelphiLogo44>Bitmaps\icon.png</UWP_DelphiLogo44>
        <UWP_DelphiLogo150>Bitmaps\icon.png</UWP_DelphiLogo150>
        <VerInfo_MinorVer>3</VerInfo_MinorVer>
        <Icon_MainIcon>SLManager_Icon.ico</Icon_MainIcon>
        <VerInfo_MajorVer>5</VerInfo_MajorVer>
        <VerInfo_AutoIncVersion>true</VerInfo_AutoIncVersion>
        <DCC_Define>madExcept;ScannerHook;CR11;UseDMS;RELEASE;StoreLogix;LVS;ConfigSetupNeu;TraceBatch;UseODAC;UsePDF;UseGnostic;TraceVerpacken;ErrorTracking;ResourceText;$(DCC_Define)</DCC_Define>
        <DCC_DebugInformation>2</DCC_DebugInformation>
        <VerInfo_Build>89</VerInfo_Build>
        <DCC_MaxStackSize>10000000</DCC_MaxStackSize>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <VerInfo_IncludeVerInfo>false</VerInfo_IncludeVerInfo>
        <DCC_LocalDebugSymbols>true</DCC_LocalDebugSymbols>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win64)'!=''">
        <AppDPIAwarenessMode>PerMonitorV2</AppDPIAwarenessMode>
        <DCC_DebugInformation>2</DCC_DebugInformation>
        <DCC_LocalDebugSymbols>true</DCC_LocalDebugSymbols>
        <DCC_RangeChecking>false</DCC_RangeChecking>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <DCC_Define>madExcept;ScannerHook;CR11;UseDMS;RELEASE;StoreLogix;LVS;ConfigSetupNeu;TraceBatch;UseODAC;TraceVerpacken;UsePDF;$(DCC_Define)</DCC_Define>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <DCC_RemoteDebug>true</DCC_RemoteDebug>
        <DCC_MaxStackSize>2048576</DCC_MaxStackSize>
        <DCC_DebugInfoInExe>true</DCC_DebugInfoInExe>
        <Version>7.0</Version>
        <DCC_IOChecking>False</DCC_IOChecking>
        <DCC_IntegerOverflowCheck>True</DCC_IntegerOverflowCheck>
        <DCC_RangeChecking>True</DCC_RangeChecking>
        <DCC_StackSize>2097152,6291456</DCC_StackSize>
        <DCC_UnitSearchPath>.\Library;.\CR 11 VCL;..\..\..\Delphi\PreView;.\Library\Jwa;.\Library\QR Code;.\ABCAnalyse;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
        <DCC_ResourcePath>.\Library;.\Library\FastMMDebug;.\Library\DCEF3\src;.\Delphi 2007;.\CR 11 VCL;..\..\Delphi\PreView;$(DCC_ResourcePath)</DCC_ResourcePath>
        <DCC_ObjPath>.\Library;.\Library\FastMMDebug;.\Library\DCEF3\src;.\Delphi 2007;.\CR 11 VCL;..\..\Delphi\PreView;$(DCC_ObjPath)</DCC_ObjPath>
        <DCC_IncludePath>.\Library;.\Library\FastMMDebug;.\Library\DCEF3\src;.\Delphi 2007;.\CR 11 VCL;..\..\Delphi\PreView;$(DCC_IncludePath)</DCC_IncludePath>
        <DCC_Define>StoreLogix;LVS;Debug;ScannerHook;CR11;UseDMS;Trace;SQLDebug;ConfigSetupNeu;TranslateHelper;UseDashBoard;madExcept;UseODAC;UseGnostic;UsePDF;DebugXML;$(DCC_Define)</DCC_Define>
        <DCC_MapFile>3</DCC_MapFile>
        <DCC_Hints>False</DCC_Hints>
        <DCC_Optimize>False</DCC_Optimize>
        <VerInfo_MinorVer>8</VerInfo_MinorVer>
        <VerInfo_Keys>CompanyName=common solutions;FileDescription=storelogix manager;FileVersion=********;InternalName=SLManager;LegalCopyright=Copyright © 2004-2020 common solution;LegalTrademarks=;OriginalFilename=SLManager.exe;ProductName=storelogix;ProductVersion=4.4</VerInfo_Keys>
        <DCC_DebugDCUs>true</DCC_DebugDCUs>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2_Win32)'!=''">
        <VerInfo_MajorVer>5</VerInfo_MajorVer>
        <VerInfo_MinorVer>2</VerInfo_MinorVer>
        <VerInfo_Release>4</VerInfo_Release>
        <VerInfo_Build>2</VerInfo_Build>
        <VerInfo_Keys>CompanyName=common solutions;FileVersion=*******;InternalName=;LegalCopyright=common solutions (R) 2024;LegalTrademarks=;OriginalFilename=;ProductVersion=5.2;Comments=;ProgramID=de.storelogix.$(MSBuildProjectName);FileDescription=$(MSBuildProjectName);ProductName=$(MSBuildProjectName)</VerInfo_Keys>
        <BT_BuildType>Debug</BT_BuildType>
        <AppDPIAwarenessMode>PerMonitor</AppDPIAwarenessMode>
        <Icon_MainIcon>SLManager_Icon.ico</Icon_MainIcon>
        <DCC_MaxStackSize>5000000</DCC_MaxStackSize>
        <DCC_MinStackSize>1000000</DCC_MinStackSize>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <DCC_DebugDCUs>false</DCC_DebugDCUs>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2_Win64)'!=''">
        <AppEnableRuntimeThemes>true</AppEnableRuntimeThemes>
        <AppDPIAwarenessMode>PerMonitorV2</AppDPIAwarenessMode>
    </PropertyGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType>VCLApplication</Borland.ProjectType>
        <BorlandProject>
            <Delphi.Personality>
                <Parameters>
                    <Parameters Name="DebugSourceDirs">D:\Projekte\Zimbo-LVS\FrontEnd\Library\</Parameters>
                    <Parameters Name="UseLauncher">False</Parameters>
                    <Parameters Name="LoadAllSymbols">True</Parameters>
                    <Parameters Name="LoadUnspecifiedSymbols">False</Parameters>
                </Parameters>
                <VersionInfo>
                    <VersionInfo Name="IncludeVerInfo">True</VersionInfo>
                    <VersionInfo Name="AutoIncBuild">False</VersionInfo>
                    <VersionInfo Name="MajorVer">4</VersionInfo>
                    <VersionInfo Name="MinorVer">4</VersionInfo>
                    <VersionInfo Name="Release">7</VersionInfo>
                    <VersionInfo Name="Build">87</VersionInfo>
                    <VersionInfo Name="Debug">False</VersionInfo>
                    <VersionInfo Name="PreRelease">False</VersionInfo>
                    <VersionInfo Name="Special">False</VersionInfo>
                    <VersionInfo Name="Private">False</VersionInfo>
                    <VersionInfo Name="DLL">False</VersionInfo>
                    <VersionInfo Name="Locale">1031</VersionInfo>
                    <VersionInfo Name="CodePage">1252</VersionInfo>
                </VersionInfo>
                <VersionInfoKeys>
                    <VersionInfoKeys Name="CompanyName">common solutions</VersionInfoKeys>
                    <VersionInfoKeys Name="FileDescription">storelogix manager</VersionInfoKeys>
                    <VersionInfoKeys Name="FileVersion">********</VersionInfoKeys>
                    <VersionInfoKeys Name="InternalName">SLManager</VersionInfoKeys>
                    <VersionInfoKeys Name="LegalCopyright">Copyright © 2004-2016 common solution</VersionInfoKeys>
                    <VersionInfoKeys Name="LegalTrademarks"/>
                    <VersionInfoKeys Name="OriginalFilename">SLManager.exe</VersionInfoKeys>
                    <VersionInfoKeys Name="ProductName">storelogix</VersionInfoKeys>
                    <VersionInfoKeys Name="ProductVersion">4.4</VersionInfoKeys>
                </VersionInfoKeys>
                <Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dcl31w290.bpl">Delphi 1.0 Kompatibilitätskomponenten</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dclbindcompdbx290.bpl">LiveBindings-Ausdruckskomponenten - DbExpress</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\DataExplorerFireDACPlugin290.bpl">FireDAC Daten-Explorer-Integration</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dclBindCompFireDAC290.bpl">LiveBinding-Ausdruckskomponenten für FireDac</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dclFMXFireDAC290.bpl">Embarcadero FMX-FireDAC-Komponenten</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dclVclFireDAC290.bpl">Embarcadero VCL-FireDAC-Komponenten</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dclFMXtee9290.bpl">TeeChart Lite FMX Components</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dclfmxstd290.bpl">Embarcadero FMX-Standardkomponenten</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\fmxstyledesigner290.bpl">Embarcadero Stil-Designer-Package</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\Skia.Package.FMX.Designtime290.bpl">Skia4Delphi-FMX-Entwurfszeit-Package</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dclsoapmidas290.bpl">Embarcadero SOAP-Midas-Komponenten</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dclsoapserver290.bpl">Embarcadero SOAP-Serverkomponenten</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dcloffice2k290.bpl">Microsoft Office 2000 Beispiele für gekapselte Komponenten für Automatisierungsserver</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dclofficexp290.bpl">Microsoft Office XP Beispiele für gekapselte Komponenten für Automation Server</Excluded_Packages>
                </Excluded_Packages>
                <Source>
                    <Source Name="MainSource">SLManager.dpr</Source>
                </Source>
            </Delphi.Personality>
            <Platforms>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">True</Platform>
            </Platforms>
            <Deployment Version="4">
                <DeployFile LocalName="..\Embarcadero\Studio\Projekte\StorelogixUnitTest\Bitmaps\icon.png" Configuration="Release" Class="UWP_DelphiLogo44">
                    <Platform Name="Win32">
                        <RemoteDir>Assets\</RemoteDir>
                        <RemoteName>Logo44x44.png</RemoteName>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="Bitmaps\icon.png" Configuration="Release" Class="UWP_DelphiLogo150"/>
                <DeployFile LocalName="Bitmaps\icon.png" Configuration="Release" Class="UWP_DelphiLogo44">
                    <Platform Name="Win32">
                        <RemoteDir>Assets\</RemoteDir>
                        <RemoteName>Logo44x44.png</RemoteName>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="SLManager.exe" Configuration="Debug" Class="ProjectOutput"/>
                <DeployFile LocalName="SLManager.exe" Configuration="Release" Class="ProjectOutput"/>
                <DeployFile LocalName="Win32\Release\Exe\SLManager.exe" Configuration="Release" Class="ProjectOutput">
                    <Platform Name="Win32">
                        <RemoteName>SLManager.exe</RemoteName>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployClass Name="AdditionalDebugSymbols">
                    <Platform Name="OSX32">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidClasses">
                    <Platform Name="Android">
                        <RemoteDir>classes</RemoteDir>
                        <Operation>64</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>classes</RemoteDir>
                        <Operation>64</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidFileProvider">
                    <Platform Name="Android">
                        <RemoteDir>res\xml</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\xml</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidLibnativeArmeabiFile">
                    <Platform Name="Android">
                        <RemoteDir>library\lib\armeabi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\armeabi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidLibnativeArmeabiv7aFile">
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\armeabi-v7a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidLibnativeMipsFile">
                    <Platform Name="Android">
                        <RemoteDir>library\lib\mips</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\mips</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidServiceOutput">
                    <Platform Name="Android">
                        <RemoteDir>library\lib\armeabi-v7a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\arm64-v8a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidServiceOutput_Android32">
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\armeabi-v7a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidSplashImageDef">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidSplashImageDefV21">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-anydpi-v21</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-anydpi-v21</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidSplashStyles">
                    <Platform Name="Android">
                        <RemoteDir>res\values</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\values</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidSplashStylesV21">
                    <Platform Name="Android">
                        <RemoteDir>res\values-v21</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\values-v21</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidSplashStylesV31">
                    <Platform Name="Android">
                        <RemoteDir>res\values-v31</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\values-v31</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_AdaptiveIcon">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-anydpi-v26</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-anydpi-v26</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_AdaptiveIconBackground">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_AdaptiveIconForeground">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_AdaptiveIconMonochrome">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_AdaptiveIconV33">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-anydpi-v33</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-anydpi-v33</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_Colors">
                    <Platform Name="Android">
                        <RemoteDir>res\values</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\values</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_ColorsDark">
                    <Platform Name="Android">
                        <RemoteDir>res\values-night-v21</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\values-night-v21</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_DefaultAppIcon">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon144">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon192">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xxxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xxxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon36">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-ldpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-ldpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon48">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-mdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-mdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon72">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-hdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-hdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon96">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_NotificationIcon24">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-mdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-mdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_NotificationIcon36">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-hdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-hdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_NotificationIcon48">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_NotificationIcon72">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_NotificationIcon96">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xxxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xxxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_SplashImage426">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-small</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-small</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_SplashImage470">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-normal</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-normal</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_SplashImage640">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-large</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-large</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_SplashImage960">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xlarge</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xlarge</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_Strings">
                    <Platform Name="Android">
                        <RemoteDir>res\values</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\values</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_VectorizedNotificationIcon">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-anydpi-v24</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-anydpi-v24</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_VectorizedSplash">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_VectorizedSplashDark">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-night-anydpi-v21</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-night-anydpi-v21</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_VectorizedSplashV31">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-anydpi-v31</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-anydpi-v31</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_VectorizedSplashV31Dark">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-night-anydpi-v31</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-night-anydpi-v31</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DebugSymbols">
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="OSX32">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DependencyFramework">
                    <Platform Name="OSX32">
                        <Operation>1</Operation>
                        <Extensions>.framework</Extensions>
                    </Platform>
                    <Platform Name="OSX64">
                        <Operation>1</Operation>
                        <Extensions>.framework</Extensions>
                    </Platform>
                    <Platform Name="OSXARM64">
                        <Operation>1</Operation>
                        <Extensions>.framework</Extensions>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DependencyModule">
                    <Platform Name="OSX32">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="OSX64">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="OSXARM64">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                        <Extensions>.dll;.bpl</Extensions>
                    </Platform>
                </DeployClass>
                <DeployClass Required="true" Name="DependencyPackage">
                    <Platform Name="iOSDevice32">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="iOSDevice64">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="OSX32">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="OSX64">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="OSXARM64">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                        <Extensions>.bpl</Extensions>
                    </Platform>
                </DeployClass>
                <DeployClass Name="File">
                    <Platform Name="Android">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="iOSDevice32">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="iOSDevice64">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="OSX32">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="OSX64">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="OSXARM64">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectAndroidManifest">
                    <Platform Name="Android">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectOSXDebug"/>
                <DeployClass Name="ProjectOSXEntitlements"/>
                <DeployClass Name="ProjectOSXInfoPList"/>
                <DeployClass Name="ProjectOSXResource">
                    <Platform Name="OSX32">
                        <RemoteDir>Contents\Resources</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="OSX64">
                        <RemoteDir>Contents\Resources</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="OSXARM64">
                        <RemoteDir>Contents\Resources</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Required="true" Name="ProjectOutput">
                    <Platform Name="Android">
                        <RemoteDir>library\lib\armeabi-v7a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\arm64-v8a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSDevice32">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSDevice64">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Linux64">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="OSX32">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="OSX64">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="OSXARM64">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectOutput_Android32">
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\armeabi-v7a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectUWPManifest">
                    <Platform Name="Win32">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win64">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectiOSDeviceDebug">
                    <Platform Name="iOSDevice32">
                        <RemoteDir>..\$(PROJECTNAME).app.dSYM\Contents\Resources\DWARF</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).app.dSYM\Contents\Resources\DWARF</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).app.dSYM\Contents\Resources\DWARF</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectiOSEntitlements"/>
                <DeployClass Name="ProjectiOSInfoPList"/>
                <DeployClass Name="ProjectiOSLaunchScreen"/>
                <DeployClass Name="ProjectiOSResource">
                    <Platform Name="iOSDevice32">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSDevice64">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="UWP_DelphiLogo150">
                    <Platform Name="Win32">
                        <RemoteDir>Assets</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win64">
                        <RemoteDir>Assets</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="UWP_DelphiLogo44">
                    <Platform Name="Win32">
                        <RemoteDir>Assets</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win64">
                        <RemoteDir>Assets</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iOS_AppStore1024">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_AppIcon152">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_AppIcon167">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_Launch2x">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_LaunchDark2x">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_Notification40">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_Setting58">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_SpotLight80">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_AppIcon120">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_AppIcon180">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Launch2x">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Launch3x">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_LaunchDark2x">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_LaunchDark3x">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Notification40">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Notification60">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Setting58">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Setting87">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Spotlight120">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Spotlight80">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <ProjectRoot Platform="Android" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="Android64" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="iOSDevice32" Name="$(PROJECTNAME).app"/>
                <ProjectRoot Platform="iOSDevice64" Name="$(PROJECTNAME).app"/>
                <ProjectRoot Platform="iOSSimARM64" Name="$(PROJECTNAME).app"/>
                <ProjectRoot Platform="iOSSimulator" Name="$(PROJECTNAME).app"/>
                <ProjectRoot Platform="Linux64" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="OSX32" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="OSX64" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="OSXARM64" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="Win32" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="Win64" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="Win64x" Name="$(PROJECTNAME)"/>
            </Deployment>
            <ModelSupport>False</ModelSupport>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets"/>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <RcCompile Include="storelogix.rc">
            <Form>storelogix.res</Form>
        </RcCompile>
        <DCCReference Include="SprachDLG.pas">
            <Form>ChangeSpracheForm</Form>
        </DCCReference>
        <DCCReference Include="LVSFrontEndMain.pas">
            <Form>LVSForm</Form>
        </DCCReference>
        <DCCReference Include="DatenModul.pas">
            <Form>LVSDatenModul</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="BestellVereinnahmung.pas">
            <Form>BestellVereinnahmungForm</Form>
        </DCCReference>
        <DCCReference Include="ArtikelDLG.pas">
            <Form>ArtikelForm</Form>
        </DCCReference>
        <DCCReference Include="SprachModul.pas">
            <Form>LVSSprachModul</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="SetupDLG.pas">
            <Form>SetupForm</Form>
        </DCCReference>
        <DCCReference Include="ConfigModul.pas">
            <Form>LVSConfigModul</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="StoreLogixAboutUnit.pas">
            <Form>AboutForm</Form>
        </DCCReference>
        <DCCReference Include="LVSSecurity.pas">
            <Form>LVSSecurityModule</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="PrintModul.pas">
            <Form>PrintModule</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="MessageModul.pas">
            <Form>MessageModule</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="MessageExDLG.pas">
            <Form>MessageForm</Form>
        </DCCReference>
        <DCCReference Include="ErrorTracking.pas">
            <Form>ErrorTrackingModule</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="DBGridUtilModule.pas">
            <Form>DBGridUtils</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="DrawLPDLG.pas">
            <Form>DrawLPForm</Form>
        </DCCReference>
        <DCCReference Include="FrontendACOModul.pas">
            <Form>FrontendACOModule</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="ACOModul.pas">
            <Form>ACOModule</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="FrontendDatasets.pas">
            <Form>FrontendDataModule</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="KommAbschlussDLG.pas">
            <Form>KommAbschlussForm</Form>
        </DCCReference>
        <DCCReference Include="KommGwAbschlussDLG.pas">
            <Form>KommGwAbschlussForm</Form>
        </DCCReference>
        <DCCReference Include="InvPosRes.pas">
            <Form>InvPosResForm</Form>
        </DCCReference>
        <DCCReference Include="SetRecordFilter.pas">
            <Form>RecordFilterForm</Form>
        </DCCReference>
        <DCCReference Include="FrontendImageModule.pas">
            <Form>ImageModule</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="PDFPreviewDLG.pas"/>
        <DCCReference Include="Library\KeyboardUtils.pas"/>
        <DCCReference Include="TourInfoFrame.pas">
            <Form>TourInfo</Form>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="ACLTemplate.pas">
            <Form>ACLFrame</Form>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="FrontendODACDatasets.pas">
            <Form>FrontendODACDataModule</Form>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="Verladegruppen\VerladegruppenDLG.pas">
            <Form>VerladegruppenMainForm</Form>
        </DCCReference>
        <DCCReference Include="Verladegruppen\LVSVerladegruppenInterface.pas"/>
        <DCCReference Include="Verladegruppen\EditVerladegruppeDLG.pas">
            <Form>EditVerladegruppeForm</Form>
        </DCCReference>
        <DCCReference Include="ImportArNachschubDLG.pas">
            <Form>ImportArNachschubForm</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="SpedSendConfigDLG.pas">
            <Form>SpedSendConfigForm</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="SendMailDLG.pas">
            <Form>SendMailForm</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="MandAdrFRM.pas">
            <Form>MandAdrFrame</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="AuftragAdrFRM.pas">
            <Form>AuftragAdrFrame</Form>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="ChromiumDLG.pas">
            <Form>ChromiumForm</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="LVSDatenInterface.pas"/>
        <RcCompile Include="aVersionInfo.rc">
            <Form>aVersionInfo.res</Form>
        </RcCompile>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Release">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
    <Import Project="$(MSBuildProjectName).deployproj" Condition="Exists('$(MSBuildProjectName).deployproj')"/>
    <PropertyGroup Condition="'$(Config)'=='Release' And '$(Platform)'=='Win32'">
        <PreBuildEvent>$(Projectdir)\set_product_version.bat
</PreBuildEvent>
        <PreBuildEventIgnoreExitCode>False</PreBuildEventIgnoreExitCode>
        <PreLinkEvent/>
        <PreLinkEventIgnoreExitCode>False</PreLinkEventIgnoreExitCode>
        <PostBuildEvent/>
        <PostBuildEventIgnoreExitCode>False</PostBuildEventIgnoreExitCode>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' And '$(Platform)'=='Win32'">
        <PreBuildEvent>$(Projectdir)\set_product_version.bat
</PreBuildEvent>
        <PreBuildEventIgnoreExitCode>False</PreBuildEventIgnoreExitCode>
        <PreLinkEvent/>
        <PreLinkEventIgnoreExitCode>False</PreLinkEventIgnoreExitCode>
        <PostBuildEvent/>
        <PostBuildEventIgnoreExitCode>False</PostBuildEventIgnoreExitCode>
    </PropertyGroup>
</Project>
