//*****************************************************************************
//*  Program System    : ACOModul
//*  Module Name       : UserDLG
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/ACOModul.pas $
// $Revision: 25 $
// $Modtime: 30.12.17 16:53 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Verwaltung der AccessControls einer Anwendung
//*****************************************************************************
unit ACOModul;

interface

uses
  SysUtils, Classes, ACOList, Menus, StdCtrls, ComCtrls, Forms;

type
  TACOModule = class(TDataModule)
  protected
    fAppID          : String;
    fACOListManager : TACOListManager;
  public
    property ACOListManager : TACOListManager read fACOListManager write fACOListManager;

    procedure CheckBerechtigungen    (const AppName : String; var ChangeFlag : Boolean);
    procedure SetBerechtigungen      (Form : TForm = Nil);

    function  CheckModificationRecht (const FormName : String; const Button : TButton) : Boolean; overload;
    function  CheckModificationRecht (const FormName : String; const Tab : TTabSheet) : Boolean; overload;
//    function  CheckModificationRecht (const FormName : String; const Tab : TAdvOfficePage) : Boolean; overload;
    function  CheckModificationRecht (const FormName : String; const MenuItem : TMenuItem) : Boolean; overload;
  published
    property ApplicationID : String read fAppID write fAppID;
  end;

procedure Register;

function UpdateACOComponentPath(const ObjectName: String; const CompPath: String): Integer;

implementation

{$R *.dfm}

uses
  Windows, Controls, DB, ADODB, DatenModul, LVSSecurity, LVSBenutzer, Dialogs, ExtCtrls;

procedure Register;
begin
  RegisterComponents('c+s', [TACOModule]);
end;


//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOModule.CheckBerechtigungen (const AppName : String; var ChangeFlag : Boolean);
var
  i,
  res     : Integer;
  found   : Boolean;
  acolist : TACOList;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CheckComponent : String;
  var
    i,
    ref     : Integer;
    typestr,
    aconame : String;
    entry   : TACOListEntry;
    query   : TADOQuery;
  begin
    query := TADOQuery.Create (Self);

    try
      query.LockType   :=ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      if (Length (ApplicationID) > 0) then begin
        res := CheckACO (query, ApplicationID, ref);

        if (res = 0) and (ref = -1) then begin
          entry := TACOListEntry.Create;

          entry.ACOType   := 'APP';
          entry.ACOObject := ApplicationID;
          entry.ACOName   := ApplicationID;
          entry.ACOText   := 'Anwendung ' + ApplicationID;

          acolist.Add (entry);
        end;
      end;

      if (ACOListManager.ACOList.Count > 0) Then begin
        for i := 0 to ACOListManager.ACOList.Count - 1 do begin
          aconame := '';

          if (ACOListManager.ACOList.Items [i].CompType = 'ACO') then begin
            typestr := 'ACO';

            if (Length (ACOListManager.ACOList.Items [i].FormName) = 0) then
              aconame := ACOListManager.ACOList.Items [i].CompName
            else
              aconame := ACOListManager.ACOList.Items [i].FormName + '.' + ACOListManager.ACOList.Items [i].CompName;
          end else if (ACOListManager.ACOList.Items [i].CompType = 'TTabSheet') then begin
            typestr := 'TAB';
            aconame := ACOListManager.ACOList.Items [i].FormName + '.' + ACOListManager.ACOList.Items [i].CompName;
          end else if (ACOListManager.ACOList.Items [i].CompType = 'TButton') then begin
            typestr := 'BUTTON';
            aconame := ACOListManager.ACOList.Items [i].FormName + '.' + ACOListManager.ACOList.Items [i].CompName;
          end else if (ACOListManager.ACOList.Items [i].CompType = 'TMenuItem') then begin
            typestr := 'MENU';
            aconame := ACOListManager.ACOList.Items [i].FormName + '.Menue.' + ACOListManager.ACOList.Items [i].CompName;
          end else if (ACOListManager.ACOList.Items [i].CompType = 'TPanel') then begin
            typestr := 'PANEL';
            aconame := ACOListManager.ACOList.Items [i].FormName + '.Panel.' + ACOListManager.ACOList.Items [i].CompName;
          end else if (ACOListManager.ACOList.Items [i].CompType = 'TForm') then begin
            typestr := 'FORM';
            aconame := ACOListManager.ACOList.Items [i].FormName + '.From.' + ACOListManager.ACOList.Items [i].CompName;
          end;

          if (Length (aconame) > 0) then begin
            entry := TACOListEntry.Create;

            if (ACOListManager.ACOList.Items [i].ACOID > 0) then begin
              res := CheckACO (query, ACOListManager.ACOList.Items [i].ACOID, entry);

              if (entry.ACORef = -1) then begin
                found := false;

                res := CheckACO (query, aconame, ref);

                entry.ACORef   := ref;

                entry.ACOID     := ACOListManager.ACOList.Items [i].ACOID;
                entry.ACOType   := typestr;
                entry.ACOObject := aconame;

                entry.ACOName  := ACOListManager.ACOList.Items [i].CompName;
                entry.ACOText  := ACOListManager.ACOList.Items [i].ACOText;
                entry.ACOGroup := ACOListManager.ACOList.Items [i].ACOGroup;
                entry.ComponentPath := ACOListManager.ACOList.Items [i].ComponentPath;
              end else begin
                found := True;

                if (entry.ACOName <> ACOListManager.ACOList.Items [i].CompName) then begin
                  found := false;

                  entry.ACOName   := ACOListManager.ACOList.Items [i].CompName;
                  entry.ACOObject := aconame;
                end;

                if (entry.ACOText <> ACOListManager.ACOList.Items [i].ACOText) then begin
                  found := false;

                  entry.ACOText   := ACOListManager.ACOList.Items [i].ACOText;
                end;

                if (entry.ACOGroup <> ACOListManager.ACOList.Items [i].ACOGroup) then begin
                  found := false;

                  entry.ACOGroup   := ACOListManager.ACOList.Items [i].ACOGroup;
                end;

                if (entry.ComponentPath <> ACOListManager.ACOList.Items [i].ComponentPath) then begin
                  found := false;

                  entry.ComponentPath := ACOListManager.ACOList.Items [i].ComponentPath;
                end;
              end;
            end else begin
              res := CheckACO (query, aconame, ref);

              found := (ref > 0);

              entry.ACORef   := ref;

              entry.ACOID    := ACOListManager.ACOList.Items [i].ACOID;
              entry.ACOType   := typestr;
              entry.ACOObject := aconame;

              entry.ACOName  := ACOListManager.ACOList.Items [i].CompName;
              entry.ACOText  := ACOListManager.ACOList.Items [i].ACOText;
              entry.ACOGroup := ACOListManager.ACOList.Items [i].ACOGroup;
              entry.ComponentPath := ACOListManager.ACOList.Items [i].ComponentPath;
            end;

            if (res = 0) and not (found) Then
              acolist.Add (entry)
            else
              entry.Free;
          end;
        end;
      end;
    finally
      query.Free;
    end;
  end;

begin
  ChangeFlag := False;

  ApplicationID := AppName;

  if (LVSDatenModul.AktUser = LVSDatenModul.Schema) then begin
    acolist := TACOList.Create;

    CheckComponent;

    if (ACOlist.Count > 0) Then begin
      if (MessageDLG ('Es gibt '+ IntToStr (acolist.Count) + ' neue Objekte, die sich geändert haben oder nicht in der ACO-Liste stehen'+#13+'Sollen diese hinzugefügt bzw. upgedatet werden ?',mtConfirmation, mbYesNoCancel, 0) = mrYes) then begin
        i := 0;

        ChangeFlag := True;

        LVSDatenModul.BeginTransaction (LVSDatenModul.MainADOConnection);

        try
          while (res = 0) and (i < acolist.Count) do begin
            if (acolist [i].ACORef = -1) then begin
              if (Length(acolist [i].ComponentPath) > 0) then
                res := InsertACO (acolist [i].ACOID, acolist [i].ACOType, ApplicationID, acolist [i].ACOObject, acolist [i].ACOGroup, acolist [i].ACOName, acolist [i].ACOText, acolist [i].ComponentPath)
              else
                res := InsertACO (acolist [i].ACOID, acolist [i].ACOType, ApplicationID, acolist [i].ACOObject, acolist [i].ACOGroup, acolist [i].ACOName, acolist [i].ACOText);
              if (res = 0) then
                res := SetUserRechte (LVSDatenModul.AktUser, ApplicationID, acolist [i].ACOObject, [Read, Write, Exec, Admin, Grant]);
            end else begin
              if (Length(acolist [i].ComponentPath) > 0) then
                res := UpdateACO (acolist [i].ACORef, acolist [i].ACOID, acolist [i].ACOObject, acolist [i].ACOGroup, acolist [i].ACOName, acolist [i].ACOText, acolist [i].ComponentPath)
              else
                res := UpdateACO (acolist [i].ACORef, acolist [i].ACOID, acolist [i].ACOObject, acolist [i].ACOGroup, acolist [i].ACOName, acolist [i].ACOText);
            end;

            Inc (i);
          end;
        except
          res := -9;
        end;

        if (res = 0) Then
          LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trCommit)
        else LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);
      end;
    end;

    ACOlist.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TACOModule.CheckModificationRecht (const FormName : String; const Button : TButton) : Boolean;
var
  recht : TLVSRechte;
begin
  if (LVSDatenModul.MainADOConnection.Connected) then begin
    if (Write in AktMandantRecht) or (Admin in AktMandantRecht) then begin
      GetRechte (LVSDatenModul.AktUser, FormName + '.' + Button.Name, recht);

      if (recht = []) then
        Result := True
      else Result := (Exec in recht) or (Admin in Recht);
    end else Result := False;
  end else Result := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TACOModule.CheckModificationRecht (const FormName : String; const Tab : TTabSheet) : Boolean;
var
  recht : TLVSRechte;
begin
  if (LVSDatenModul.MainADOConnection.Connected) then begin
    if (Write in AktMandantRecht) or (Admin in AktMandantRecht) then begin
      GetRechte (LVSDatenModul.AktUser, FormName + '.' + Tab.Name, recht);

      Result := (Write in recht) or (Admin in Recht);
    end else Result := False;
  end else Result := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
(*
function TACOModule.CheckModificationRecht (const FormName : String; const Tab : TAdvOfficePage) : Boolean;
var
  recht : TLVSRechte;
begin
  if (LVSDatenModul.MainADOConnection.Connected) then begin
    if (Write in AktMandantRecht) or (Admin in AktMandantRecht) then begin
      GetRechte (LVSDatenModul.AktUser, FormName + '.' + Tab.Name, recht);

      Result := (Write in recht) or (Admin in Recht);
    end else Result := False;
  end else Result := False;
end;
*)

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TACOModule.CheckModificationRecht (const FormName : String; const MenuItem : TMenuItem) : Boolean;
var
  recht : TLVSRechte;
begin
  if (LVSDatenModul.MainADOConnection.Connected) then begin
    if (Write in AktMandantRecht) or (Admin in AktMandantRecht) then begin
      GetRechte (LVSDatenModul.AktUser, FormName + '.Menue.' + MenuItem.Name, recht);

      Result := (Exec in recht) or (Admin in Recht);
    end else Result := False;
  end else Result := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TACOModule.SetBerechtigungen (Form : TForm);

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CheckAccessRecht (ACOName : String) : Boolean;
  var
    recht : TLVSRechte;
  begin
    if (Read in AktMandantRecht) or (Admin in AktMandantRecht) then begin
      GetRechte (LVSDatenModul.AktUser, ACOName, recht);

      Result := (Exec in recht) or (Read in Recht);
    end else Result := False;
  end;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CheckWriteRecht (ACOName : String) : Boolean;
  var
    recht : TLVSRechte;
  begin
    if (Write in AktMandantRecht) or (Admin in AktMandantRecht) then begin
      GetRechte (LVSDatenModul.AktUser, ACOName, recht);

      Result := (Write in Recht);
    end else Result := False;
  end;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CheckAdminRecht (ACOName : String) : Boolean; overload;
  var
    recht : TLVSRechte;
  begin
    GetRechte (LVSDatenModul.AktUser, ACOName, recht);

    Result := (Admin in recht);
  end;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function CheckExecuteRecht (ACOName : String) : Boolean; overload;
  var
    recht : TLVSRechte;
  begin
    GetRechte (LVSDatenModul.AktUser, ACOName, recht);

    Result := (Exec in recht);
  end;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function FindComponente (const FormName, CompName : String) : TComponent; overload;
  var
    cidx,
    fidx  : Integer;
    stop,
    found : Boolean;
    comp  : TComponent;
  begin
    fidx := 0;

    comp  := Nil;
    stop  := False;
    found := False;

    while (fidx < Application.ComponentCount) and not (stop) do begin
      if Application.Components[fidx] is TForm then begin
        if (Application.Components[fidx].Name = FormName) then begin
          with Application.Components[fidx] as TForm do begin
            cidx := 0;

            while (cidx < ComponentCount) and not (found) do begin
              if (Components [cidx].Name = CompName) then begin
                comp := Components [cidx];
                found := True;
              end else Inc (cidx);
            end;
          end;

          stop := True;
        end;
      end;

      if not (stop) then
        Inc (fidx);
    end;

    if (found) then
      Result := comp
    else Result := Nil;
  end;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  function FindComponente (SuchForm : TForm; const CompName : String) : TComponent; overload;
  var
    cidx  : Integer;
    found : Boolean;
    comp  : TComponent;
  begin
    comp  := Nil;
    found := False;

    cidx := 0;

    while (cidx < SuchForm.ComponentCount) and not (found) do begin
      if (SuchForm.Components [cidx].Name = CompName) then begin
        comp := SuchForm.Components [cidx];
        found := True;
      end else Inc (cidx);
    end;

    if (found) then
      Result := comp
    else
      Result := Nil;
  end;


  function GetComponentPath(Component: TComponent): String;
  var
    PathParts: TStringList;
    CurrentComp: TComponent;
    CurrentMenuItem: TMenuItem;
    MainMenu: TMainMenu;
    i: Integer;
  begin
    Result := '';
    if not Assigned(Component) then Exit;

    PathParts := TStringList.Create;
    try
      CurrentComp := Component;

      // Spezielle Behandlung für TMenuItem
      if (CurrentComp is TMenuItem) then begin
        CurrentMenuItem := CurrentComp as TMenuItem;

        // Sammle MenuItem-Hierarchie
        while Assigned(CurrentMenuItem) do begin
          PathParts.Insert(0, CurrentMenuItem.Name);
          CurrentMenuItem := CurrentMenuItem.Parent;
        end;

        // Finde das Hauptmenü
        if (Component is TMenuItem) then begin
          MainMenu := (Component as TMenuItem).GetParentMenu;
          if Assigned(MainMenu) then begin
            PathParts.Insert(0, MainMenu.Name);

            // Finde die Form des Hauptmenüs
            if Assigned(MainMenu.Owner) and (MainMenu.Owner is TForm) then
              PathParts.Insert(0, MainMenu.Owner.Name);
          end;
        end;
      end else begin
        // Normale Control-Hierarchie
        while Assigned(CurrentComp) do begin
          PathParts.Insert(0, CurrentComp.Name);

          if (CurrentComp is TControl) and Assigned((CurrentComp as TControl).Parent) then
            CurrentComp := (CurrentComp as TControl).Parent
          else if Assigned(CurrentComp.Owner) and not (CurrentComp.Owner is TForm) then
            CurrentComp := CurrentComp.Owner
          else
            Break;
        end;
      end;

      for i := 0 to PathParts.Count - 1 do begin
        if i > 0 then Result := Result + '.';
        Result := Result + PathParts[i];
      end;
    finally
      PathParts.Free;
    end;
  end;

  //******************************************************************************
  //* Function Name:
  //* Author       : Stefan Graf
  //******************************************************************************
  //* Description
  //******************************************************************************
  //* Return Value :
  //******************************************************************************
  procedure SetControlRechte (Control : TWinControl; const FormName : String; const Recht : Boolean);
  var
    cidx : Integer;
  begin
    cidx := 0;

    while (cidx < Control.ControlCount) do begin
      if (Control.Controls [cidx] is TButton) then begin
        if (ACOListManager.ACOList.FindACO (FormName, Control.Controls [cidx].Name) = Nil) then
          (Control.Controls [cidx] as TButton).Enabled := Recht;
      end;

      if (Control.Controls [cidx] is TWinControl) then
        SetControlRechte ((Control.Controls [cidx] as TWinControl), FormName, Recht);

      Inc (cidx);
    end;
  end;

var
  idx    : Integer;
  button : TButton;
  tab    : TTabSheet;
//  page   : TAdvOfficePage;
  menu   : TMenuItem;
  panel  : TPanel;
  compo  : TComponent;
begin
  idx := 0;

  while (idx < ACOListManager.ACOList.Count) do begin
    with ACOListManager.ACOList.Items [idx] do begin
      if not Assigned (Form) or (Form.Name = ACOListManager.ACOList.Items [idx].FormName) then begin
        if (ACOListManager.ACOList.Items [idx].CompType = 'TButton') then begin
          if Assigned (Form) then
            button := (FindComponente (Form, CompName) as TButton)
          else
            button := (FindComponente (FormName, CompName) as TButton);

          if Assigned (button) then begin
            if (Length(ComponentPath) = 0) then begin
              ComponentPath := GetComponentPath(button);
              ACOListManager.ACOList.Items [idx].ComponentPath := ComponentPath;

              if (Length(ComponentPath) > 0) then begin
                UpdateACOComponentPath(ACOListManager.ACOList.Items [idx].ACOObject, ComponentPath);
              end;
            end;

            if not (CheckAccessRecht (ACOName)) then
              button.Visible := False
            else begin
              button.Visible := True;
              button.Enabled := CheckAccessRecht (ACOName);
            end;
          end;
        end else if (ACOListManager.ACOList.Items [idx].CompType = 'TTabSheet') then begin
          if Assigned (Form) then
            compo := FindComponente (Form, CompName)
          else
            compo := FindComponente (FormName, CompName);

          if (compo is TTabSheet) then begin
            tab := (compo as TTabSheet);

            if Assigned (tab) then begin
              if (Length(ComponentPath) = 0) then begin
                ComponentPath := GetComponentPath(tab);
                ACOListManager.ACOList.Items [idx].ComponentPath := ComponentPath;

                if (Length(ComponentPath) > 0) then begin
                  UpdateACOComponentPath(ACOListManager.ACOList.Items [idx].ACOObject, ComponentPath);
                end;
              end;

              if not (CheckAccessRecht (ACOName)) then begin
                tab.Enabled := False;
                tab.TabVisible := False;
              end else begin
                if (CheckWriteRecht (ACOName)) Then
                  SetControlRechte (tab, FormName, True)
                else SetControlRechte (tab, FormName, False);

                tab.Enabled := True;
                tab.TabVisible := True;
              end;
            end;
  (*
          end else if (compo is TAdvOfficePage) then begin
            page := (compo as TAdvOfficePage);

            if Assigned (page) then begin
              if not (CheckAccessRecht (ACOName)) then begin
                page.Enabled := False;
                page.TabVisible := False;
              end else begin
                if (CheckWriteRecht (ACOName)) Then
                  SetControlRechte (page, FormName, True)
                else SetControlRechte (page, FormName, False);

                page.Enabled := True;
                page.TabVisible := True;
              end;
            end;
  *)
          end;
        end else if (ACOListManager.ACOList.Items [idx].CompType = 'TMenuItem') then begin
          if Assigned (Form) then
            menu := (FindComponente (Form, CompName) as TMenuItem)
          else
            menu := (FindComponente (FormName, CompName) as TMenuItem);

          if Assigned (menu) then begin
            if (Length(ComponentPath) = 0) then begin
              ComponentPath := GetComponentPath(menu);
              ACOListManager.ACOList.Items [idx].ComponentPath := ComponentPath;

              if (Length(ComponentPath) > 0) then begin
                UpdateACOComponentPath(ACOListManager.ACOList.Items [idx].ACOObject, ComponentPath);
              end;
            end;

            menu.Visible := CheckExecuteRecht (FormName + '.Menue.' + CompName);
          end;
        end else if (ACOListManager.ACOList.Items [idx].CompType = 'TPanel') then begin
          if Assigned (Form) then
            panel := (FindComponente (Form, CompName) as TPanel)
          else
            panel := (FindComponente (FormName, CompName) as TPanel);

          if Assigned (panel) then begin
            if (Length(ComponentPath) = 0) then begin
              ComponentPath := GetComponentPath(panel);
              ACOListManager.ACOList.Items [idx].ComponentPath := ComponentPath;

              if (Length(ComponentPath) > 0) then begin
                UpdateACOComponentPath(ACOListManager.ACOList.Items [idx].ACOObject, ComponentPath);
              end;
            end;

            panel.Enabled := false;
            panel.Visible := false;

            if CheckWriteRecht (FormName + '.Panel.' + CompName) then begin
              panel.Visible := true;
              panel.Enabled := true
            end else if CheckAccessRecht (FormName + '.Panel.' + CompName) then begin
              panel.Visible := true;
            end;
          end;
        end;
      end;

      Inc (idx);
    end;
  end;
end;

function UpdateACOComponentPath(const ObjectName: String; const CompPath: String): Integer;
  var
    query: TADOQuery;
  begin
    Result := 0;

    if (Length(ObjectName) = 0) or (Length(CompPath) = 0) then begin
      Result := -1;
      Exit;
    end;

    query := TADOQuery.Create(nil);
    try
      query.Connection := LVSDatenModul.MainADOConnection;

      try
        query.SQL.Add('UPDATE SYS_ACO SET COMPONENT_PATH = :comp_path WHERE OBJECT_NAME = :obj_name AND APPLICATION = :app');
        query.Parameters.ParamByName('comp_path').Value := CompPath;
        query.Parameters.ParamByName('obj_name').Value := ObjectName;

        query.ExecSQL;
      except
        on E: Exception do begin
          Result := -1;
        end;
      end;
    finally
      query.Free;
    end;
  end;

end.
